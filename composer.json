{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "codedge/laravel-fpdf": "^1.12", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^0.6.8", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.19", "laravel/tinker": "^2.8", "maatwebsite/excel": "^3.1", "spatie/laravel-backup": "^8.5", "spatie/laravel-medialibrary": "^10.15", "spatie/laravel-query-builder": "^6.0", "tightenco/ziggy": "^1.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.26", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helper/StatusHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://satis.spatie.be"}]}