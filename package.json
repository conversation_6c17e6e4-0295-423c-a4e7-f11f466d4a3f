{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build && vite build --ssr"}, "devDependencies": {"@inertiajs/vue3": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^4.0.0", "@vue/server-renderer": "^3.2.31", "autoprefixer": "^10.4.12", "axios": "^1.6.1", "laravel-vite-plugin": "^0.8.0", "mini-svg-data-uri": "^1.4.4", "postcss": "^8.4.18", "tailwindcss": "^3.2.1", "vite": "^4.0.0", "vue": "^3.2.41"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.5.0", "@lottiefiles/dotlottie-vue": "^0.1.8", "@tinymce/tinymce-vue": "^5.1.1", "@vueform/multiselect": "^2.6.6", "aos": "^2.3.4", "bootstrap": "^5.3.3", "chart.js": "^4.4.1", "datatables.net-bs5": "^2.1.6", "datatables.net-buttons-bs5": "^3.1.2", "datatables.net-dt": "^2.1.6", "datatables.net-responsive-bs5": "^3.0.3", "datatables.net-responsive-dt": "^3.0.3", "datatables.net-select-dt": "^2.1.0", "datatables.net-vue3": "^3.0.2", "file-saver": "^2.0.5", "jquery": "^3.7.1", "jszip": "^3.10.1", "owl.carousel": "^2.3.4", "sass": "^1.69.5", "uuid": "^9.0.1", "vue-chartjs": "^5.3.0", "vue3-carousel": "^0.3.1"}}