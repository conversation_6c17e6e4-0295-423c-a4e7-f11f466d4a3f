<?php

namespace App\Helper;

class StatusHelper
{
    public static function getStatus($status): string
    {
        switch ($status) {
            case 'waiting':
                return '<PERSON>unggu';
            case 'submitted':
                return '<PERSON><PERSON><PERSON><PERSON><PERSON>';
            case 'approved':
                return '<PERSON><PERSON><PERSON><PERSON><PERSON>';
            case 'rejected':
                return '<PERSON><PERSON><PERSON>';
            default:
                return $status;
        }
    }
}
