<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Models\Wave;
use Inertia\Inertia;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request; // Import Request
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage; // Import Storage

class DaftarPesertaContoller extends Controller
{

    protected $tahun_akademik;

    public function index()
    {
        $peserta = User::with(['payments', 'getForm.kelas', 'getForm.prodi', 'getForm.wave'])
            ->whereHas('payments', function ($query) {
                $query->where('type_payment', 'form')
                    ->where('status', 'approved');
            })->whereHas('getForm', function ($query) {
                $query->where('nim', null);
            })
            ->latest()
            ->get();

        // dd($peserta->first()->getForm->getFirstMediaUrl('foto'));

        $wave = Wave::select('tahun_akademik')
            ->distinct()
            ->get();

        return Inertia::render('Admin/DaftarPeserta', [
            'peserta' => $peserta,
            'wave' => $wave
        ]);
    }

    /**
     * [BARU] Method untuk mengambil data detail seorang peserta.
     * Method ini akan dipanggil oleh Axios dari frontend.
     *
     * @param \App\Models\User $peserta
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(User $peserta)
    {
        // Load relasi yang diperlukan
        $peserta->load(['payments', 'getForm.kelas', 'getForm.prodi', 'getForm.wave']);

        // Ambil form yang sudah di-load
        $form = $peserta->getForm;

        if ($form) {
            // Tambahkan URL media dari setiap koleksi
            $form->ktp_url = $form->getFirstMediaUrl('ktp');
            $form->foto_url = $form->getFirstMediaUrl('foto');
            $form->ijazah_url = $form->getFirstMediaUrl('ijazah');
            $form->kk_url = $form->getFirstMediaUrl('kartu_keluarga');
        }

        return response()->json($peserta);
    }



    public function laporanAll()
    {
        // ... (tidak ada perubahan di method ini)
        $peserta = User::with(['payments', 'getForm.kelas', 'getForm.prodi', 'getForm.wave'])
            ->whereHas('payments', function ($query) {
                $query->where('type_payment', 'registration')
                    ->where('status', 'approved');
            })
            ->latest()
            ->get();

        $wave = Wave::select('tahun_akademik')
            ->distinct()
            ->get();

        return Inertia::render('Admin/DaftarPeserta', [
            'peserta' => $peserta,
            'wave' => $wave
        ]);
    }

    public function destroy(User $peserta)
    {
        if ($peserta->getForm) {
            $peserta->getForm()->delete();
        }

        $peserta->payments()->delete();
        $peserta->delete();

        return Redirect::back()->with('success', 'Data peserta berhasil dihapus.');
    }
}
