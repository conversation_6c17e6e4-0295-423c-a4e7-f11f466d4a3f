<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        $faker = Faker::create('id_ID');

        \App\Models\User::factory()->create([
            'name' => $faker->name,
            'email' => '<EMAIL>',
            'phone' => $faker->phoneNumber,
            'email_verified_at' => $faker->dateTime,
            'password' => Hash::make('password'),
            'remember_token' => $faker->password,
            'is_Banned' => false,
            'created_at' => $faker->dateTime,
            'updated_at' => $faker->dateTime,
        ]);
    }
}
